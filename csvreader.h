#ifndef CSVREADER_H
#define CSVREADER_H

#include <QObject>
#include <QVariantList>
#include <QDate>
#include <QString>

class CsvReader : public QObject
{
    Q_OBJECT

public:
    explicit CsvReader(QObject *parent = nullptr);

    // 历史数据读取功能
    Q_INVOKABLE QVariantList readDataByDate(const QDate &date);
    Q_INVOKABLE QVariantList getAvailableDates();
    Q_INVOKABLE bool hasDataForDate(const QDate &date);
    Q_INVOKABLE QVariantList readDataByDateRange(const QDate &startDate, const QDate &endDate);

private:
    QString buildFilePath(const QDate &date);
    QVariantMap parseDataLine(const QString &line, const QStringList &headers);
};

#endif // CSVREADER_H