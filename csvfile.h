#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <time.h>


// CSV文件管理结构体
typedef struct {
    FILE *file;             // 当前打开的文件指针
    char filename[128];     // 当前文件名
    time_t start_time;      // 文件创建时间
    size_t current_size;    // 当前文件大小（字节）
    size_t max_size;        // 文件最大大小（字节）,0不生效
    char buffer[8192];      // 缓冲区
    size_t buffer_size;     // 缓冲区当前大小
    size_t flush_threshold; // 缓冲区刷新阈值
    time_t last_flush_time; // 上次刷新时间
    int flush_interval;     // 刷新间隔（秒）
    char fileprefix[128];   //文件名前缀
    time_t last_check_time; // 上次检查的时间（时间1）
    time_t current_check_time; // 当前检查的时间（时间2）
} csv_manager_t;

int init_csv_manager(csv_manager_t *csv, size_t max_size, size_t flush_threshold, int flush_interval, const char *fileprefix);
int create_new_csv_file(csv_manager_t *csv);
int need_new_date_file(csv_manager_t *csv);
int write_to_csv_buffer(csv_manager_t *csv, const unsigned char *data, size_t length);
int flush_csv_buffer(csv_manager_t *csv);
void close_csv_manager(csv_manager_t *csv);


